/* Enhanced Global Styles */
:root {
    --primary-color: #0066cc;
    --primary-dark: #004499;
    --primary-light: #3388dd;
    --secondary-color: #00a86b;
    --secondary-dark: #007a4f;
    --secondary-light: #33bb88;
    --accent-color: #ff6b6b;
    --accent-dark: #ff4444;
    --accent-light: #ff8888;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333;
    --text-light: #666;
    --text-muted: #999;
    --white: #ffffff;
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
    --transition: all 0.3s ease;
    --transition-fast: all 0.2s ease;
    --transition-slow: all 0.5s ease;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 20px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
    overflow-x: hidden;
    position: relative;
}



.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

h1, h2, h3, h4 {
    margin-bottom: 15px;
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
    color: var(--primary-color);
}

h3 {
    font-size: 1.5rem;
    color: var(--dark-color);
}

h4 {
    font-size: 1.2rem;
    color: var(--dark-color);
}

p {
    margin-bottom: 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
    padding-bottom: 15px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
}

.btn {
    display: inline-block;
    padding: 12px 25px;
    border-radius: 5px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn.primary {
    background-color: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background-color: #0052a3;
    transform: translateY(-3px);
}

.btn.secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn.secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn.card-btn {
    margin-top: 15px;
    font-size: 0.9rem;
}

/* Header Styles */
header {
    background-color: white;
    box-shadow: var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.5rem;
    color: var(--dark-color);
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 25px;
}

nav ul li a {
    font-weight: 500;
    color: var(--dark-color);
    position: relative;
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

nav ul li a:hover::after {
    width: 100%;
}

.lang-toggle {
    background-color: var(--primary-color);
    color: white;
    padding: 5px 12px;
    border-radius: 4px;
}

.lang-toggle:hover {
    background-color: #0052a3;
    color: white;
}

.mobile-menu-btn {
    display: none;
    font-size: 1.5rem;
    color: var(--dark-color);
    cursor: pointer;
}

/* Enhanced Header Styles */
.header-enhanced {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
}

.header-enhanced.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    transition: var(--transition);
}

.logo-icon:hover {
    transform: scale(1.1);
}

.logo-text h1 {
    font-size: 1.4rem;
    color: var(--dark-color);
    margin: 0;
    line-height: 1.2;
}

.logo-text span {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 400;
}

.nav-enhanced ul {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--dark-color);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.nav-link.active {
    background: var(--primary-color);
    color: var(--white);
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    padding: 10px 0;
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    color: var(--dark-color);
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.mobile-menu-btn {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
    padding: 10px;
}

.mobile-menu-btn span {
    width: 25px;
    height: 3px;
    background: var(--dark-color);
    border-radius: 2px;
    transition: var(--transition);
}

.mobile-menu-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Enhanced Hero Section */
.hero-enhanced {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    display: flex;
    align-items: center;
    padding: 120px 0 80px;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-icon {
    position: absolute;
    font-size: 2rem;
    color: rgba(0, 102, 204, 0.1);
    animation: float 6s ease-in-out infinite;
}

.floating-icon:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-icon:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
}

.floating-icon:nth-child(3) {
    top: 30%;
    right: 15%;
    animation-delay: 2s;
}

.floating-icon:nth-child(4) {
    bottom: 30%;
    left: 20%;
    animation-delay: 3s;
}

.floating-icon:nth-child(5) {
    bottom: 20%;
    right: 25%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

.hero-enhanced .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-content {
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--gradient-primary);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 20px;
}

.hero-title {
    margin-bottom: 25px;
}

.title-main {
    display: block;
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--dark-color);
    line-height: 1.1;
    margin-bottom: 10px;
}

.title-sub {
    display: block;
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--text-light);
    line-height: 1.3;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 40px;
    line-height: 1.7;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-top: 5px;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn.enhanced {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn.enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn.enhanced:hover::before {
    left: 100%;
}

.btn.primary.enhanced {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn.primary.enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn.secondary.enhanced {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn.secondary.enhanced:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn.tertiary.enhanced {
    background: transparent;
    color: var(--dark-color);
    border: 2px solid var(--dark-color);
}

.btn.tertiary.enhanced:hover {
    background: var(--dark-color);
    color: var(--white);
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image-container {
    position: relative;
    width: 300px;
    height: 300px;
}

.hero-main-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--white);
    box-shadow: var(--shadow-xl);
    animation: pulse 3s infinite;
}

.orbit-icons {
    position: relative;
    width: 100%;
    height: 100%;
}

.orbit-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    animation: orbit 10s linear infinite;
}

.orbit-1 {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.orbit-2 {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    animation-delay: -3.33s;
}

.orbit-3 {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: -6.66s;
}

@keyframes orbit {
    from {
        transform: rotate(0deg) translateX(120px) rotate(0deg);
    }
    to {
        transform: rotate(360deg) translateX(120px) rotate(-360deg);
    }
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    box-shadow: var(--shadow);
    animation: bounce 2s infinite;
    cursor: pointer;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image i {
    font-size: 15rem;
    color: var(--primary-color);
    opacity: 0.8;
}

/* Modules Section */
.modules {
    padding: 80px 0;
    background-color: var(--light-color);
}

.module-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-icon {
    margin-bottom: 20px;
}

.card-icon i {
    font-size: 3rem;
    color: var(--primary-color);
}

.card h3 {
    margin-bottom: 15px;
}

.card p {
    margin-bottom: 20px;
    color: #666;
}

/* Enhanced Modules Section */
.modules-enhanced {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-top: 10px;
    font-weight: 400;
}

.module-cards-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
}

.enhanced-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-slow);
    position: relative;
    transform-style: preserve-3d;
}

.enhanced-card:hover {
    box-shadow: var(--shadow-xl);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.05;
    z-index: 1;
}

.card-pattern {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, var(--primary-color) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, var(--secondary-color) 2px, transparent 2px);
    background-size: 50px 50px;
}

.card-content {
    position: relative;
    z-index: 2;
    padding: 40px 30px;
}

.enhanced-card .card-icon {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
}

.icon-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.3;
    filter: blur(15px);
    z-index: -1;
}

.card-badge {
    display: inline-block;
    background: var(--gradient-secondary);
    color: var(--white);
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.enhanced-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--dark-color);
    font-weight: 700;
}

.enhanced-card p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 25px;
}

.card-features {
    margin-bottom: 25px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.feature-item i {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

.card-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--light-color);
    border-radius: var(--border-radius);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-light);
    margin-top: 5px;
}

.btn.card-btn.enhanced {
    width: 100%;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    padding: 15px 25px;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn.card-btn.enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn.card-btn.enhanced:hover::before {
    left: 100%;
}

.btn.card-btn.enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 20px;
    max-width: 400px;
    z-index: 10001;
    transform: translateX(100%);
    transition: var(--transition);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notification-success {
    border-left: 4px solid var(--secondary-color);
}

.notification-error {
    border-left: 4px solid var(--accent-color);
}

.notification-info {
    border-left: 4px solid var(--primary-color);
}

.notification i {
    font-size: 1.2rem;
}

.notification-success i {
    color: var(--secondary-color);
}

.notification-error i {
    color: var(--accent-color);
}

.notification-info i {
    color: var(--primary-color);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .hero-enhanced .container {
        gap: 40px;
    }

    .title-main {
        font-size: 3rem;
    }

    .module-cards-enhanced {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 30px;
    }
}

@media (max-width: 992px) {
    .hero-enhanced .container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 50px;
    }

    .hero-stats {
        justify-content: center;
    }

    .cta-buttons {
        justify-content: center;
    }

    .nav-enhanced ul {
        gap: 20px;
    }

    .nav-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .header-enhanced .container {
        padding: 10px 15px;
    }

    .logo-text h1 {
        font-size: 1.2rem;
    }

    .logo-text span {
        font-size: 0.8rem;
    }

    .nav-enhanced {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .hero-enhanced {
        padding: 100px 0 60px;
        min-height: auto;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .btn.enhanced {
        padding: 12px 25px;
        font-size: 0.9rem;
    }

    .module-cards-enhanced {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .enhanced-card .card-content {
        padding: 30px 20px;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .title-main {
        font-size: 2rem;
    }

    .title-sub {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 15px;
    }

    .floating-icon {
        display: none;
    }

    .hero-image-container {
        width: 250px;
        height: 250px;
    }

    .hero-main-icon {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
}

/* Modal and Popup Styles */
.hospital-design-area {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.8) !important;
    z-index: 10000 !important;
    display: none !important;
    justify-content: center !important;
    align-items: center !important;
}

.design-content {
    background: var(--white) !important;
    border-radius: var(--border-radius-lg) !important;
    padding: 30px !important;
    max-width: 90% !important;
    max-height: 90% !important;
    overflow-y: auto !important;
    position: relative !important;
}

.close-design {
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    background: var(--accent-color) !important;
    color: var(--white) !important;
    border: none !important;
    border-radius: 50% !important;
    width: 35px !important;
    height: 35px !important;
    font-size: 1.2rem !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: var(--transition) !important;
}

.close-design:hover {
    background: var(--accent-dark) !important;
    transform: scale(1.1) !important;
}

/* Features Section */
.features {
    padding: 80px 0;
    background-color: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-item {
    text-align: center;
    padding: 20px;
}

.feature-icon {
    margin-bottom: 20px;
}

.feature-icon i {
    font-size: 2.5rem;
    color: var(--secondary-color);
}

.feature-item h3 {
    margin-bottom: 15px;
}

/* Learning Path Section */
.learning-path {
    padding: 80px 0;
    background-color: var(--light-color);
}

.path-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.path-steps::before {
    content: '';
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
    height: 4px;
    background-color: #ddd;
    z-index: 1;
}

.step {
    flex: 1;
    text-align: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 auto 20px;
    position: relative;
}

.step-number::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.3;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background-color: white;
}

.testimonial-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background-color: var(--light-color);
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
}

.testimonial-content {
    margin-bottom: 20px;
    font-style: italic;
    color: #555;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-image i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-right: 15px;
}

.author-info h4 {
    margin-bottom: 5px;
}

.author-info p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

/* About Section */
.about {
    padding: 80px 0;
    background-color: var(--light-color);
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.about-text {
    flex: 1;
}

.about-image {
    flex: 1;
    text-align: center;
}

.about-image i {
    font-size: 15rem;
    color: var(--primary-color);
    opacity: 0.8;
}

.author-info {
    display: flex;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.author-image i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-right: 15px;
}

.author-details h3 {
    margin-bottom: 5px;
}

.author-details p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background-color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 15px;
    margin-top: 5px;
}

.contact-item h3 {
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.contact-form {
    background-color: var(--light-color);
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: inherit;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 150px;
}

/* Expert Guidance Section */
.expert-guidance {
    padding: 80px 0;
    background-color: var(--light-color);
}

.guidance-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    font-size: 1.1rem;
    color: #555;
}

.guidance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.guidance-item {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.guidance-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.guidance-icon {
    text-align: center;
    margin-bottom: 20px;
}

.guidance-icon i {
    font-size: 3rem;
    color: var(--primary-color);
}

.guidance-item h3 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.guidance-details h4 {
    font-size: 1.1rem;
    margin: 20px 0 10px;
    color: var(--primary-color);
}

.guidance-details ul {
    list-style-type: none;
    margin-bottom: 15px;
}

.guidance-details li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: flex-start;
}

.guidance-details li::before {
    content: "•";
    color: var(--secondary-color);
    font-weight: bold;
    margin-right: 10px;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-column h3 {
    color: white;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--secondary-color);
}

.footer-column p,
.footer-column li {
    margin-bottom: 10px;
    color: #ddd;
}

.footer-column ul li a {
    color: #ddd;
}

.footer-column ul li a:hover {
    color: white;
    padding-left: 5px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #aaa;
    font-size: 0.9rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        padding-right: 0;
        margin-bottom: 40px;
    }

    .hero-image i {
        font-size: 10rem;
    }

    .about-content {
        flex-direction: column;
    }

    .about-image {
        order: -1;
    }

    .about-image i {
        font-size: 10rem;
    }
}

@media (max-width: 768px) {
    nav ul {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: white;
        flex-direction: column;
        align-items: center;
        padding: 20px 0;
        box-shadow: var(--shadow);
        transform: translateY(-150%);
        transition: var(--transition);
    }

    nav ul.active {
        transform: translateY(0);
    }

    nav ul li {
        margin: 10px 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero-content h2 {
        font-size: 2rem;
    }

    .path-steps {
        flex-direction: column;
    }

    .path-steps::before {
        display: none;
    }

    .step {
        margin-bottom: 30px;
    }

    .step-number::after {
        display: none;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .hero-content h2 {
        font-size: 1.8rem;
    }

    .cta-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .logo h1 {
        font-size: 1.2rem;
    }

    .hero-image i {
        font-size: 8rem;
    }

    .about-image i {
        font-size: 8rem;
    }
}
