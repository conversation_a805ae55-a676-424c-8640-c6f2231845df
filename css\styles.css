/* Global Styles */
:root {
    --primary-color: #0066cc;
    --secondary-color: #00a86b;
    --accent-color: #ff6b6b;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

h1, h2, h3, h4 {
    margin-bottom: 15px;
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
    color: var(--primary-color);
}

h3 {
    font-size: 1.5rem;
    color: var(--dark-color);
}

h4 {
    font-size: 1.2rem;
    color: var(--dark-color);
}

p {
    margin-bottom: 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
    padding-bottom: 15px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
}

.btn {
    display: inline-block;
    padding: 12px 25px;
    border-radius: 5px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn.primary {
    background-color: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background-color: #0052a3;
    transform: translateY(-3px);
}

.btn.secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn.secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn.card-btn {
    margin-top: 15px;
    font-size: 0.9rem;
}

/* Header Styles */
header {
    background-color: white;
    box-shadow: var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.5rem;
    color: var(--dark-color);
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 25px;
}

nav ul li a {
    font-weight: 500;
    color: var(--dark-color);
    position: relative;
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

nav ul li a:hover::after {
    width: 100%;
}

.lang-toggle {
    background-color: var(--primary-color);
    color: white;
    padding: 5px 12px;
    border-radius: 4px;
}

.lang-toggle:hover {
    background-color: #0052a3;
    color: white;
}

.mobile-menu-btn {
    display: none;
    font-size: 1.5rem;
    color: var(--dark-color);
    cursor: pointer;
}

/* Hero Section */
.hero {
    padding: 180px 0 80px;
    background-color: white;
    margin-top: 0;
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-content {
    flex: 1;
    padding-right: 30px;
}

.hero-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.hero-content p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    max-width: 600px;
}

.cta-buttons {
    display: flex;
    gap: 15px;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image i {
    font-size: 15rem;
    color: var(--primary-color);
    opacity: 0.8;
}

/* Modules Section */
.modules {
    padding: 80px 0;
    background-color: var(--light-color);
}

.module-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-icon {
    margin-bottom: 20px;
}

.card-icon i {
    font-size: 3rem;
    color: var(--primary-color);
}

.card h3 {
    margin-bottom: 15px;
}

.card p {
    margin-bottom: 20px;
    color: #666;
}

/* Features Section */
.features {
    padding: 80px 0;
    background-color: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-item {
    text-align: center;
    padding: 20px;
}

.feature-icon {
    margin-bottom: 20px;
}

.feature-icon i {
    font-size: 2.5rem;
    color: var(--secondary-color);
}

.feature-item h3 {
    margin-bottom: 15px;
}

/* Learning Path Section */
.learning-path {
    padding: 80px 0;
    background-color: var(--light-color);
}

.path-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.path-steps::before {
    content: '';
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
    height: 4px;
    background-color: #ddd;
    z-index: 1;
}

.step {
    flex: 1;
    text-align: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 auto 20px;
    position: relative;
}

.step-number::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.3;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background-color: white;
}

.testimonial-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background-color: var(--light-color);
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
}

.testimonial-content {
    margin-bottom: 20px;
    font-style: italic;
    color: #555;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-image i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-right: 15px;
}

.author-info h4 {
    margin-bottom: 5px;
}

.author-info p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

/* About Section */
.about {
    padding: 80px 0;
    background-color: var(--light-color);
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.about-text {
    flex: 1;
}

.about-image {
    flex: 1;
    text-align: center;
}

.about-image i {
    font-size: 15rem;
    color: var(--primary-color);
    opacity: 0.8;
}

.author-info {
    display: flex;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.author-image i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-right: 15px;
}

.author-details h3 {
    margin-bottom: 5px;
}

.author-details p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background-color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 15px;
    margin-top: 5px;
}

.contact-item h3 {
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.contact-form {
    background-color: var(--light-color);
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: inherit;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 150px;
}

/* Expert Guidance Section */
.expert-guidance {
    padding: 80px 0;
    background-color: var(--light-color);
}

.guidance-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    font-size: 1.1rem;
    color: #555;
}

.guidance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.guidance-item {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.guidance-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.guidance-icon {
    text-align: center;
    margin-bottom: 20px;
}

.guidance-icon i {
    font-size: 3rem;
    color: var(--primary-color);
}

.guidance-item h3 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.guidance-details h4 {
    font-size: 1.1rem;
    margin: 20px 0 10px;
    color: var(--primary-color);
}

.guidance-details ul {
    list-style-type: none;
    margin-bottom: 15px;
}

.guidance-details li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: flex-start;
}

.guidance-details li::before {
    content: "•";
    color: var(--secondary-color);
    font-weight: bold;
    margin-right: 10px;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-column h3 {
    color: white;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--secondary-color);
}

.footer-column p,
.footer-column li {
    margin-bottom: 10px;
    color: #ddd;
}

.footer-column ul li a {
    color: #ddd;
}

.footer-column ul li a:hover {
    color: white;
    padding-left: 5px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #aaa;
    font-size: 0.9rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        padding-right: 0;
        margin-bottom: 40px;
    }

    .hero-image i {
        font-size: 10rem;
    }

    .about-content {
        flex-direction: column;
    }

    .about-image {
        order: -1;
    }

    .about-image i {
        font-size: 10rem;
    }
}

@media (max-width: 768px) {
    nav ul {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: white;
        flex-direction: column;
        align-items: center;
        padding: 20px 0;
        box-shadow: var(--shadow);
        transform: translateY(-150%);
        transition: var(--transition);
    }

    nav ul.active {
        transform: translateY(0);
    }

    nav ul li {
        margin: 10px 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero-content h2 {
        font-size: 2rem;
    }

    .path-steps {
        flex-direction: column;
    }

    .path-steps::before {
        display: none;
    }

    .step {
        margin-bottom: 30px;
    }

    .step-number::after {
        display: none;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .hero-content h2 {
        font-size: 1.8rem;
    }

    .cta-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .logo h1 {
        font-size: 1.2rem;
    }

    .hero-image i {
        font-size: 8rem;
    }

    .about-image i {
        font-size: 8rem;
    }
}
