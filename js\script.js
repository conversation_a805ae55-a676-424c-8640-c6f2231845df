// Mobile Menu Toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('nav ul');

    mobileMenuBtn.addEventListener('click', function() {
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    const navLinks = document.querySelectorAll('nav ul li a');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('active');
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                window.scrollTo({
                    top: target.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('header');
        if (window.scrollY > 100) {
            header.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
        }
    });

    // Active navigation link based on scroll position
    const sections = document.querySelectorAll('section');
    const navItems = document.querySelectorAll('nav ul li a');

    window.addEventListener('scroll', () => {
        let current = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (pageYOffset >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('href').substring(1) === current) {
                item.classList.add('active');
            }
        });
    });

    // Form submission
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;

            // Simple form validation
            if (!name || !email || !subject || !message) {
                showNotification('Please fill in all fields', 'error');
                return;
            }

            // Here you would normally send the form data to a server
            // For demo purposes, we'll just show a success message
            showNotification('Your message has been sent successfully!', 'success');
            contactForm.reset();
        });
    }

    // Notification function
    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // Add styles
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.padding = '15px 25px';
        notification.style.borderRadius = '5px';
        notification.style.color = 'white';
        notification.style.fontWeight = '500';
        notification.style.zIndex = '10000';
        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        notification.style.transform = 'translateX(400px)';
        notification.style.transition = 'transform 0.3s ease';

        if (type === 'success') {
            notification.style.backgroundColor = '#00a86b';
        } else if (type === 'error') {
            notification.style.backgroundColor = '#ff6b6b';
        }

        // Add to DOM
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(400px)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Animation on scroll
    const animateElements = document.querySelectorAll('.card, .feature-item, .step, .testimonial-card');

    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    animateElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        observer.observe(element);
    });

    // Module card interactions
    const moduleCards = document.querySelectorAll('.module-cards .card');
    moduleCards.forEach(card => {
        card.addEventListener('click', function() {
            const moduleTitle = this.querySelector('h3').textContent;
            showNotification(`Opening ${moduleTitle} module...`, 'success');
            // In a real application, this would navigate to the module
        });
    });

    // Learning path interaction
    const pathSteps = document.querySelectorAll('.step');
    pathSteps.forEach((step, index) => {
        step.addEventListener('click', function() {
            // Remove active class from all steps
            pathSteps.forEach(s => s.classList.remove('active'));

            // Add active class to clicked step
            this.classList.add('active');

            const stepTitle = this.querySelector('h3').textContent;
            showNotification(`Viewing ${stepTitle} resources`, 'success');
        });
    });

    // Language toggle
    const langToggle = document.querySelector('.lang-toggle');
    if (langToggle) {
        langToggle.addEventListener('click', function(e) {
            e.preventDefault();
            const currentLang = this.textContent;

            if (currentLang === 'العربية') {
                this.textContent = 'English';
                showNotification('Language changed to Arabic', 'success');
                // In a real application, this would load Arabic content
            } else {
                this.textContent = 'العربية';
                showNotification('Language changed to English', 'success');
                // In a real application, this would load English content
            }
        });
    }

    // Simulate drag and drop functionality for hospital design
    const hospitalDesignArea = document.createElement('div');
    hospitalDesignArea.className = 'hospital-design-area';
    hospitalDesignArea.style.display = 'none';
    hospitalDesignArea.style.position = 'fixed';
    hospitalDesignArea.style.top = '0';
    hospitalDesignArea.style.left = '0';
    hospitalDesignArea.style.width = '100%';
    hospitalDesignArea.style.height = '100%';
    hospitalDesignArea.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    hospitalDesignArea.style.zIndex = '1000';
    hospitalDesignArea.style.display = 'flex';
    hospitalDesignArea.style.justifyContent = 'center';
    hospitalDesignArea.style.alignItems = 'center';

    const designContent = document.createElement('div');
    designContent.className = 'design-content';
    designContent.style.backgroundColor = 'white';
    designContent.style.width = '90%';
    designContent.style.maxWidth = '1000px';
    designContent.style.height = '80vh';
    designContent.style.borderRadius = '10px';
    designContent.style.padding = '20px';
    designContent.style.position = 'relative';

    const closeDesign = document.createElement('button');
    closeDesign.className = 'close-design';
    closeDesign.textContent = '×';
    closeDesign.style.position = 'absolute';
    closeDesign.style.top = '10px';
    closeDesign.style.right = '10px';
    closeDesign.style.fontSize = '2rem';
    closeDesign.style.background = 'none';
    closeDesign.style.border = 'none';
    closeDesign.style.cursor = 'pointer';

    designContent.appendChild(closeDesign);
    hospitalDesignArea.appendChild(designContent);
    document.body.appendChild(hospitalDesignArea);

    // Event listener for hospital design module
    const designModule = document.querySelector('.module-cards .card:nth-child(1) .card-btn');
    if (designModule) {
        designModule.addEventListener('click', function(e) {
            e.preventDefault();
            hospitalDesignArea.style.display = 'flex';
            designContent.innerHTML = `
                <h2>Hospital Design Simulation</h2>
                <p>Drag and drop elements to design your hospital layout.</p>
                <div class="design-tools" style="display: flex; margin-bottom: 20px; gap: 10px;">
                    <button class="design-tool" data-tool="room">Add Room</button>
                    <button class="design-tool" data-tool="equipment">Add Equipment</button>
                    <button class="design-tool" data-tool="corridor">Add Corridor</button>
                </div>
                <div class="design-canvas" style="width: 100%; height: 70%; border: 2px dashed #ccc; border-radius: 5px; position: relative;">
                    <p style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #999;">Drag elements here to start designing</p>
                </div>
            `;
            designContent.appendChild(closeDesign);

            // Add event listeners for design tools
            const designTools = designContent.querySelectorAll('.design-tool');
            designTools.forEach(tool => {
                tool.style.padding = '8px 15px';
                tool.style.backgroundColor = '#0066cc';
                tool.style.color = 'white';
                tool.style.border = 'none';
                tool.style.borderRadius = '5px';
                tool.style.cursor = 'pointer';

                tool.addEventListener('click', function() {
                    const toolType = this.getAttribute('data-tool');
                    showNotification(`${toolType} tool selected`, 'success');
                });
            });

            // Close design area
            closeDesign.addEventListener('click', function() {
                hospitalDesignArea.style.display = 'none';
            });
        });
    }
});
