<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Equipment Planning - Virtual Hospital Training Platform</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .module-content {
            padding: 120px 0 80px;
        }

        .module-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .module-header h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .module-header p {
            max-width: 800px;
            margin: 0 auto;
            font-size: 1.1rem;
            color: #555;
        }

        .equipment-showcase {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 40px;
        }

        .showcase-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .showcase-title {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .showcase-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .control-btn:hover {
            background-color: #0052a3;
        }

        .equipment-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }

        .category-card {
            flex: 1;
            min-width: 200px;
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .category-card.active {
            background-color: var(--primary-color);
            color: white;
        }

        .category-card i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .category-card.active i {
            color: white;
        }

        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 25px;
        }

        .equipment-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            transition: var(--transition);
        }

        .equipment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }

        .equipment-image {
            height: 180px;
            background-color: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .equipment-image i {
            font-size: 4rem;
            color: var(--primary-color);
        }

        .equipment-info {
            padding: 20px;
        }

        .equipment-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .equipment-specs {
            margin-bottom: 15px;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        .spec-label {
            color: #666;
        }

        .spec-value {
            font-weight: 500;
        }

        .equipment-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: #0052a3;
        }

        .action-btn.secondary {
            background-color: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .action-btn.secondary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .planning-tools {
            margin-top: 60px;
        }

        .tools-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .tools-title {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .tools-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .tab-btn {
            padding: 12px 25px;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 500;
            color: #555;
            cursor: pointer;
            position: relative;
            transition: var(--transition);
        }

        .tab-btn.active {
            color: var(--primary-color);
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary-color);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .planner-interface {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .planner-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .planner-title {
            font-size: 1.5rem;
            color: var(--dark-color);
        }

        .planner-controls {
            display: flex;
            gap: 10px;
        }

        .planner-workspace {
            display: flex;
            gap: 20px;
        }

        .equipment-list {
            flex: 1;
            max-width: 300px;
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            height: 500px;
            overflow-y: auto;
        }

        .list-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        .list-items {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .list-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .list-item:hover {
            background-color: rgba(0, 102, 204, 0.1);
        }

        .list-item i {
            font-size: 1.5rem;
            margin-right: 10px;
            color: var(--primary-color);
        }

        .list-item-name {
            font-weight: 500;
        }

        .planner-canvas {
            flex: 1;
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            height: 500px;
            position: relative;
            overflow: hidden;
        }

        .canvas-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #999;
        }

        .canvas-placeholder i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ccc;
        }

        .case-studies {
            margin-top: 60px;
        }

        .case-studies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .case-study-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        .case-study-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .case-study-image {
            height: 200px;
            background-color: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .case-study-image i {
            font-size: 4rem;
            color: var(--primary-color);
        }

        .case-study-content {
            padding: 20px;
        }

        .case-study-content h3 {
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        .case-study-content p {
            color: #555;
            margin-bottom: 15px;
        }

        .resources-section {
            margin-top: 60px;
        }

        .resources-list {
            list-style-type: none;
        }

        .resources-list li {
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .resources-list li:last-child {
            border-bottom: none;
        }

        .resources-list li i {
            margin-right: 15px;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .resources-list li a {
            flex: 1;
            color: var(--text-color);
            font-weight: 500;
        }

        .resources-list li a:hover {
            color: var(--primary-color);
        }

        .quiz-section {
            margin-top: 40px;
            background-color: var(--light-color);
            padding: 30px;
            border-radius: 10px;
        }

        .quiz-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .quiz-title {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .quiz-progress {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .progress-bar {
            width: 200px;
            height: 10px;
            background-color: #ddd;
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--secondary-color);
            width: 25%;
        }

        .quiz-question {
            background-color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .question-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
            color: var(--dark-color);
        }

        .answer-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .answer-option {
            padding: 15px;
            border: 2px solid #eee;
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition);
        }

        .answer-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(0, 102, 204, 0.05);
        }

        .quiz-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .nav-btn {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .nav-btn:hover {
            background-color: #0052a3;
        }

        .nav-btn.secondary {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .nav-btn.secondary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            .showcase-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .equipment-categories {
                justify-content: center;
            }

            .planner-workspace {
                flex-direction: column;
            }

            .equipment-list {
                max-width: 100%;
                height: auto;
            }

            .planner-canvas {
                height: 300px;
            }

            .quiz-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .progress-bar {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <i class="fas fa-hospital"></i>
                <h1>Virtual Hospital Training Platform</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="facility-design.html">Healthcare Facility Design</a></li>
                    <li><a href="equipment-planning.html" class="active">Medical Equipment Planning</a></li>
                    <li><a href="clinical-management.html">Clinical Engineering Management</a></li>
                    <li><a href="#" class="lang-toggle">العربية</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <section class="module-content">
        <div class="container">
            <div class="module-header">
                <h2>Medical Equipment Planning</h2>
                <p>Learn equipment selection criteria, technical specifications, and project management for healthcare facilities through interactive simulations and real-world case studies.</p>
            </div>

            <div class="equipment-showcase">
                <div class="showcase-header">
                    <h3 class="showcase-title">Equipment Catalog</h3>
                    <div class="showcase-controls">
                        <button class="control-btn"><i class="fas fa-filter"></i> Filter</button>
                        <button class="control-btn"><i class="fas fa-search"></i> Search</button>
                    </div>
                </div>

                <div class="equipment-categories">
                    <div class="category-card active">
                        <i class="fas fa-x-ray"></i>
                        <span>Diagnostic</span>
                    </div>
                    <div class="category-card">
                        <i class="fas fa-heartbeat"></i>
                        <span>Monitoring</span>
                    </div>
                    <div class="category-card">
                        <i class="fas fa-procedures"></i>
                        <span>Treatment</span>
                    </div>
                    <div class="category-card">
                        <i class="fas fa-vial"></i>
                        <span>Laboratory</span>
                    </div>
                </div>

                <div class="equipment-grid">
                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-x-ray"></i>
                        </div>
                        <div class="equipment-info">
                            <h4 class="equipment-name">MRI Scanner</h4>
                            <div class="equipment-specs">
                                <div class="spec-item">
                                    <span class="spec-label">Field Strength:</span>
                                    <span class="spec-value">1.5T / 3.0T</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Bore Size:</span>
                                    <span class="spec-value">70 cm</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Power:</span>
                                    <span class="spec-value">50 kW</span>
                                </div>
                            </div>
                            <div class="equipment-actions">
                                <button class="action-btn primary">Details</button>
                                <button class="action-btn secondary">Compare</button>
                            </div>
                        </div>
                    </div>
                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <div class="equipment-info">
                            <h4 class="equipment-name">CT Scanner</h4>
                            <div class="equipment-specs">
                                <div class="spec-item">
                                    <span class="spec-label">Slice Thickness:</span>
                                    <span class="spec-value">0.5 - 10 mm</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Gantry Opening:</span>
                                    <span class="spec-value">78 cm</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Scan Time:</span>
                                    <span class="spec-value">0.3 s/rotation</span>
                                </div>
                            </div>
                            <div class="equipment-actions">
                                <button class="action-btn primary">Details</button>
                                <button class="action-btn secondary">Compare</button>
                            </div>
                        </div>
                    </div>
                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <div class="equipment-info">
                            <h4 class="equipment-name">Patient Monitor</h4>
                            <div class="equipment-specs">
                                <div class="spec-item">
                                    <span class="spec-label">Display:</span>
                                    <span class="spec-value">12" Touchscreen</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Parameters:</span>
                                    <span class="spec-value">ECG, SpO2, NIBP, IBP</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Battery:</span>
                                    <span class="spec-value">6 hours</span>
                                </div>
                            </div>
                            <div class="equipment-actions">
                                <button class="action-btn primary">Details</button>
                                <button class="action-btn secondary">Compare</button>
                            </div>
                        </div>
                    </div>
                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-lungs"></i>
                        </div>
                        <div class="equipment-info">
                            <h4 class="equipment-name">Ventilator</h4>
                            <div class="equipment-specs">
                                <div class="spec-item">
                                    <span class="spec-label">Modes:</span>
                                    <span class="spec-value">8+ modes</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Tidal Volume:</span>
                                    <span class="spec-value">20 - 2000 mL</span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Oxygen:</span>
                                    <span class="spec-value">21 - 100%</span>
                                </div>
                            </div>
                            <div class="equipment-actions">
                                <button class="action-btn primary">Details</button>
                                <button class="action-btn secondary">Compare</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="planning-tools">
                <div class="tools-header">
                    <h3 class="tools-title">Equipment Planning Tools</h3>
                </div>

                <div class="tools-tabs">
                    <button class="tab-btn active">Equipment Planner</button>
                    <button class="tab-btn">Budget Calculator</button>
                    <button class="tab-btn">Maintenance Scheduler</button>
                </div>

                <div class="tab-content active">
                    <div class="planner-interface">
                        <div class="planner-header">
                            <h4 class="planner-title">Department Equipment Planner</h4>
                            <div class="planner-controls">
                                <button class="control-btn"><i class="fas fa-save"></i> Save Plan</button>
                                <button class="control-btn"><i class="fas fa-file-export"></i> Export</button>
                            </div>
                        </div>

                        <div class="planner-workspace">
                            <div class="equipment-list">
                                <h5 class="list-title">Available Equipment</h5>
                                <div class="list-items">
                                    <div class="list-item">
                                        <i class="fas fa-x-ray"></i>
                                        <span class="list-item-name">MRI Scanner</span>
                                    </div>
                                    <div class="list-item">
                                        <i class="fas fa-microscope"></i>
                                        <span class="list-item-name">CT Scanner</span>
                                    </div>
                                    <div class="list-item">
                                        <i class="fas fa-heartbeat"></i>
                                        <span class="list-item-name">Patient Monitor</span>
                                    </div>
                                    <div class="list-item">
                                        <i class="fas fa-lungs"></i>
                                        <span class="list-item-name">Ventilator</span>
                                    </div>
                                    <div class="list-item">
                                        <i class="fas fa-syringe"></i>
                                        <span class="list-item-name">Infusion Pump</span>
                                    </div>
                                    <div class="list-item">
                                        <i class="fas fa-weight"></i>
                                        <span class="list-item-name">Patient Scale</span>
                                    </div>
                                </div>
                            </div>

                            <div class="planner-canvas">
                                <div class="canvas-placeholder">
                                    <i class="fas fa-hospital"></i>
                                    <p>Drag equipment items here to create your department plan</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-content">
                    <div class="planner-interface">
                        <div class="planner-header">
                            <h4 class="planner-title">Budget Calculator</h4>
                        </div>
                        <p>Calculate the total cost of equipment for your department, including purchase price, installation, maintenance, and operating costs over time.</p>
                        <div style="margin-top: 30px; text-align: center; padding: 50px 0;">
                            <i class="fas fa-calculator" style="font-size: 4rem; color: var(--primary-color); margin-bottom: 20px;"></i>
                            <p>Budget calculator tool coming soon...</p>
                        </div>
                    </div>
                </div>

                <div class="tab-content">
                    <div class="planner-interface">
                        <div class="planner-header">
                            <h4 class="planner-title">Maintenance Scheduler</h4>
                        </div>
                        <p>Plan and schedule maintenance activities for all equipment in your department to ensure optimal performance and compliance with regulatory requirements.</p>
                        <div style="margin-top: 30px; text-align: center; padding: 50px 0;">
                            <i class="fas fa-calendar-alt" style="font-size: 4rem; color: var(--primary-color); margin-bottom: 20px;"></i>
                            <p>Maintenance scheduler tool coming soon...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="case-studies">
                <h3 class="section-title">Real-World Case Studies</h3>
                <div class="case-studies-grid">
                    <div class="case-study-card">
                        <div class="case-study-image">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <div class="case-study-content">
                            <h4>Regional Hospital Equipment Upgrade</h4>
                            <p>How a 200-bed hospital successfully upgraded its diagnostic imaging equipment while maintaining continuous patient care.</p>
                            <a href="#" class="btn card-btn">Read Case Study</a>
                        </div>
                    </div>
                    <div class="case-study-card">
                        <div class="case-study-image">
                            <i class="fas fa-procedures"></i>
                        </div>
                        <div class="case-study-content">
                            <h4>ICU Equipment Standardization Project</h4>
                            <p>Implementation of standardized equipment across multiple intensive care units to improve training and reduce operational costs.</p>
                            <a href="#" class="btn card-btn">Read Case Study</a>
                        </div>
                    </div>
                    <div class="case-study-card">
                        <div class="case-study-image">
                            <i class="fas fa-vial"></i>
                        </div>
                        <div class="case-study-content">
                            <h4>Laboratory Automation Implementation</h4>
                            <p>Complete redesign of laboratory equipment workflow to increase efficiency and reduce turnaround time for critical tests.</p>
                            <a href="#" class="btn card-btn">Read Case Study</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="resources-section">
                <h3 class="section-title">Learning Resources</h3>
                <ul class="resources-list">
                    <li>
                        <i class="fas fa-book"></i>
                        <a href="#">Medical Equipment Selection Guide</a>
                    </li>
                    <li>
                        <i class="fas fa-video"></i>
                        <a href="#">Equipment Planning Video Tutorials</a>
                    </li>
                    <li>
                        <i class="fas fa-file-pdf"></i>
                        <a href="#">Technical Specifications Database</a>
                    </li>
                    <li>
                        <i class="fas fa-calculator"></i>
                        <a href="#">Cost of Ownership Calculator</a>
                    </li>
                    <li>
                        <i class="fas fa-users"></i>
                        <a href="#">Vendor Evaluation Framework</a>
                    </li>
                </ul>
            </div>

            <div class="quiz-section">
                <div class="quiz-header">
                    <h4 class="quiz-title">Knowledge Check</h4>
                    <div class="quiz-progress">
                        <span>1 of 4</span>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                    </div>
                </div>

                <div class="quiz-question">
                    <p class="question-text">Which factor is most important when selecting diagnostic imaging equipment for a hospital with limited space?</p>
                    <div class="answer-options">
                        <div class="answer-option">Maximum image resolution</div>
                        <div class="answer-option">Equipment footprint and spatial requirements</div>
                        <div class="answer-option">Brand reputation</div>
                        <div class="answer-option">Initial purchase price</div>
                    </div>
                </div>

                <div class="quiz-navigation">
                    <button class="nav-btn secondary">Previous</button>
                    <button class="nav-btn">Next Question</button>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Virtual Hospital Training Platform</h3>
                    <p>An interactive educational platform for biomedical engineering students and professionals.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Training Modules</h3>
                    <ul>
                        <li><a href="facility-design.html">Healthcare Facility Design</a></li>
                        <li><a href="equipment-planning.html">Medical Equipment Planning</a></li>
                        <li><a href="clinical-management.html">Clinical Engineering Management</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <ul>
                        <li><a href="#">Case Studies</a></li>
                        <li><a href="#">Research Papers</a></li>
                        <li><a href="#">Video Tutorials</a></li>
                        <li><a href="#">Glossary</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Contact Us</h3>
                    <p>Biomedical Engineering Department<br>Sudan University of Science and Technology</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +249 123 456 789</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Virtual Hospital Training Platform. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>