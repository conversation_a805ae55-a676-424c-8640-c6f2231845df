<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering Management - Virtual Hospital Training Platform</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .module-content {
            padding: 120px 0 80px;
        }

        .module-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .module-header h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .module-header p {
            max-width: 800px;
            margin: 0 auto;
            font-size: 1.1rem;
            color: #555;
        }

        .management-dashboard {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 40px;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .dashboard-title {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .dashboard-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .control-btn:hover {
            background-color: #0052a3;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: var(--dark-color);
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .maintenance-simulator {
            margin-top: 40px;
        }

        .simulator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .simulator-title {
            font-size: 1.5rem;
            color: var(--dark-color);
        }

        .scenario-selector {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .scenario-card {
            flex: 1;
            min-width: 200px;
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
        }

        .scenario-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .scenario-card.active {
            background-color: var(--primary-color);
            color: white;
        }

        .scenario-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .scenario-card.active .scenario-icon {
            color: white;
        }

        .scenario-workspace {
            display: flex;
            gap: 20px;
        }

        .scenario-details {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }

        .scenario-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        .scenario-description {
            margin-bottom: 20px;
            color: #555;
        }

        .scenario-steps {
            margin-bottom: 25px;
        }

        .step-item {
            display: flex;
            margin-bottom: 15px;
        }

        .step-number {
            width: 30px;
            height: 30px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--dark-color);
        }

        .step-description {
            color: #666;
            font-size: 0.95rem;
        }

        .scenario-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
        }

        .action-btn.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .action-btn.primary:hover {
            background-color: #0052a3;
        }

        .action-btn.secondary {
            background-color: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .action-btn.secondary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .tools-panel {
            width: 300px;
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            height: fit-content;
        }

        .tools-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        .tools-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .tool-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background-color: white;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .tool-item:hover {
            background-color: rgba(0, 102, 204, 0.1);
        }

        .tool-item i {
            font-size: 1.5rem;
            margin-right: 10px;
            color: var(--primary-color);
        }

        .tool-item-name {
            font-weight: 500;
        }

        .risk-management {
            margin-top: 60px;
        }

        .section-title {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            padding-bottom: 15px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: var(--secondary-color);
        }

        .risk-matrix {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 40px;
        }

        .matrix-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .matrix-row {
            display: flex;
            gap: 20px;
        }

        .matrix-cell {
            flex: 1;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .cell-low {
            background-color: rgba(0, 168, 107, 0.2);
            color: #00a86b;
        }

        .cell-medium {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }

        .cell-high {
            background-color: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }

        .matrix-cell:hover {
            transform: scale(1.05);
        }

        .matrix-legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 10px;
        }

        .case-studies {
            margin-top: 60px;
        }

        .case-studies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .case-study-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        .case-study-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .case-study-image {
            height: 200px;
            background-color: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .case-study-image i {
            font-size: 4rem;
            color: var(--primary-color);
        }

        .case-study-content {
            padding: 20px;
        }

        .case-study-content h3 {
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        .case-study-content p {
            color: #555;
            margin-bottom: 15px;
        }

        .resources-section {
            margin-top: 60px;
        }

        .resources-list {
            list-style-type: none;
        }

        .resources-list li {
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .resources-list li:last-child {
            border-bottom: none;
        }

        .resources-list li i {
            margin-right: 15px;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .resources-list li a {
            flex: 1;
            color: var(--text-color);
            font-weight: 500;
        }

        .resources-list li a:hover {
            color: var(--primary-color);
        }

        .quiz-section {
            margin-top: 40px;
            background-color: var(--light-color);
            padding: 30px;
            border-radius: 10px;
        }

        .quiz-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .quiz-title {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .quiz-progress {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .progress-bar {
            width: 200px;
            height: 10px;
            background-color: #ddd;
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--secondary-color);
            width: 25%;
        }

        .quiz-question {
            background-color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .question-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
            color: var(--dark-color);
        }

        .answer-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .answer-option {
            padding: 15px;
            border: 2px solid #eee;
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition);
        }

        .answer-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(0, 102, 204, 0.05);
        }

        .quiz-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .nav-btn {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .nav-btn:hover {
            background-color: #0052a3;
        }

        .nav-btn.secondary {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .nav-btn.secondary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        @media (max-width: 992px) {
            .scenario-workspace {
                flex-direction: column;
            }

            .tools-panel {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .scenario-selector {
                flex-direction: column;
            }

            .scenario-card {
                width: 100%;
            }

            .quiz-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .progress-bar {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <i class="fas fa-hospital"></i>
                <h1>Virtual Hospital Training Platform</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="facility-design.html">Healthcare Facility Design</a></li>
                    <li><a href="equipment-planning.html">Medical Equipment Planning</a></li>
                    <li><a href="clinical-management.html" class="active">Clinical Engineering Management</a></li>
                    <li><a href="#" class="lang-toggle">العربية</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <section class="module-content">
        <div class="container">
            <div class="module-header">
                <h2>Clinical Engineering Management</h2>
                <p>Develop skills in maintenance protocols, risk management, and department operations for clinical engineering through interactive simulations and real-world scenarios.</p>
            </div>

            <div class="management-dashboard">
                <div class="dashboard-header">
                    <h3 class="dashboard-title">Department Management Dashboard</h3>
                    <div class="dashboard-controls">
                        <button class="control-btn"><i class="fas fa-sync-alt"></i> Refresh</button>
                        <button class="control-btn"><i class="fas fa-download"></i> Export</button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <i class="fas fa-tools stat-icon"></i>
                        <div class="stat-value">98%</div>
                        <div class="stat-label">Equipment Uptime</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-clipboard-check stat-icon"></i>
                        <div class="stat-value">142</div>
                        <div class="stat-label">Completed PMs</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-exclamation-triangle stat-icon"></i>
                        <div class="stat-value">7</div>
                        <div class="stat-label">Open Work Orders</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-user-md stat-icon"></i>
                        <div class="stat-value">24</div>
                        <div class="stat-label">Staff Members</div>
                    </div>
                </div>

                <div class="maintenance-simulator">
                    <div class="simulator-header">
                        <h3 class="simulator-title">Maintenance Scenario Simulator</h3>
                        <div class="dashboard-controls">
                            <button class="control-btn"><i class="fas fa-play"></i> Start</button>
                            <button class="control-btn"><i class="fas fa-redo"></i> Reset</button>
                        </div>
                    </div>

                    <div class="scenario-selector">
                        <div class="scenario-card active">
                            <i class="fas fa-procedures scenario-icon"></i>
                            <h4>Equipment Failure</h4>
                            <p>Respond to critical equipment failure scenarios</p>
                        </div>
                        <div class="scenario-card">
                            <i class="fas fa-calendar-check scenario-icon"></i>
                            <h4>Preventive Maintenance</h4>
                            <p>Plan and execute preventive maintenance schedules</p>
                        </div>
                        <div class="scenario-card">
                            <i class="fas fa-user-cog scenario-icon"></i>
                            <h4>Staff Management</h4>
                            <p>Optimize staff allocation and training</p>
                        </div>
                    </div>

                    <div class="scenario-workspace">
                        <div class="scenario-details">
                            <h4 class="scenario-title">Ventilator Failure in ICU</h4>
                            <p class="scenario-description">A critical ventilator in the Intensive Care Unit has failed during patient use. You need to quickly diagnose the issue, implement a backup plan, and arrange for repair or replacement.</p>

                            <div class="scenario-steps">
                                <div class="step-item">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <div class="step-title">Assess the Situation</div>
                                        <div class="step-description">Gather information about the failure, patient condition, and available backup equipment.</div>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <div class="step-title">Implement Backup Plan</div>
                                        <div class="step-description">Deploy backup ventilator and ensure continuous patient care.</div>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <div class="step-title">Diagnose the Issue</div>
                                        <div class="step-description">Perform troubleshooting to identify the cause of the failure.</div>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <div class="step-number">4</div>
                                    <div class="step-content">
                                        <div class="step-title">Repair or Replace</div>
                                        <div class="step-description">Decide whether to repair on-site or replace the unit based on diagnosis.</div>
                                    </div>
                                </div>
                            </div>

                            <div class="scenario-actions">
                                <button class="action-btn primary">Start Scenario</button>
                                <button class="action-btn secondary">View Resources</button>
                            </div>
                        </div>

                        <div class="tools-panel">
                            <h4 class="tools-title">Available Tools</h4>
                            <div class="tools-list">
                                <div class="tool-item">
                                    <i class="fas fa-stethoscope"></i>
                                    <span class="tool-item-name">Diagnostic Kit</span>
                                </div>
                                <div class="tool-item">
                                    <i class="fas fa-laptop-medical"></i>
                                    <span class="tool-item-name">Service Manual</span>
                                </div>
                                <div class="tool-item">
                                    <i class="fas fa-phone-alt"></i>
                                    <span class="tool-item-name">Contact Support</span>
                                </div>
                                <div class="tool-item">
                                    <i class="fas fa-clipboard-list"></i>
                                    <span class="tool-item-name">Maintenance Log</span>
                                </div>
                                <div class="tool-item">
                                    <i class="fas fa-users"></i>
                                    <span class="tool-item-name">Staff Directory</span>
                                </div>
                                <div class="tool-item">
                                    <i class="fas fa-boxes"></i>
                                    <span class="tool-item-name">Inventory</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="risk-management">
                <h3 class="section-title">Risk Assessment Matrix</h3>
                <div class="risk-matrix">
                    <div class="matrix-container">
                        <div class="matrix-row">
                            <div class="matrix-cell cell-high">Critical</div>
                            <div class="matrix-cell cell-high">Critical</div>
                            <div class="matrix-cell cell-high">Critical</div>
                            <div class="matrix-cell cell-high">Critical</div>
                            <div class="matrix-cell cell-high">Critical</div>
                        </div>
                        <div class="matrix-row">
                            <div class="matrix-cell cell-medium">High</div>
                            <div class="matrix-cell cell-high">High</div>
                            <div class="matrix-cell cell-high">High</div>
                            <div class="matrix-cell cell-high">High</div>
                            <div class="matrix-cell cell-high">Critical</div>
                        </div>
                        <div class="matrix-row">
                            <div class="matrix-cell cell-medium">Medium</div>
                            <div class="matrix-cell cell-medium">Medium</div>
                            <div class="matrix-cell cell-high">High</div>
                            <div class="matrix-cell cell-high">High</div>
                            <div class="matrix-cell cell-high">Critical</div>
                        </div>
                        <div class="matrix-row">
                            <div class="matrix-cell cell-low">Low</div>
                            <div class="matrix-cell cell-medium">Medium</div>
                            <div class="matrix-cell cell-medium">Medium</div>
                            <div class="matrix-cell cell-high">High</div>
                            <div class="matrix-cell cell-high">High</div>
                        </div>
                        <div class="matrix-row">
                            <div class="matrix-cell cell-low">Low</div>
                            <div class="matrix-cell cell-low">Low</div>
                            <div class="matrix-cell cell-medium">Medium</div>
                            <div class="matrix-cell cell-medium">Medium</div>
                            <div class="matrix-cell cell-high">High</div>
                        </div>
                    </div>
                    <div class="matrix-legend">
                        <div class="legend-item">
                            <div class="legend-color cell-low"></div>
                            <span>Low Risk</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color cell-medium"></div>
                            <span>Medium Risk</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color cell-high"></div>
                            <span>High Risk</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="case-studies">
                <h3 class="section-title">Real-World Case Studies</h3>
                <div class="case-studies-grid">
                    <div class="case-study-card">
                        <div class="case-study-image">
                            <i class="fas fa-hospital-user"></i>
                        </div>
                        <div class="case-study-content">
                            <h4>Hospital-Wide Equipment Upgrade</h4>
                            <p>How a major hospital successfully upgraded 300+ pieces of equipment with zero downtime.</p>
                            <a href="#" class="btn primary">Read More</a>
                        </div>
                    </div>
                    <div class="case-study-card">
                        <div class="case-study-image">
                            <i class="fas fa-virus"></i>
                        </div>
                        <div class="case-study-content">
                            <h4>Pandemic Response Management</h4>
                            <p>Clinical engineering strategies for equipment management during the COVID-19 crisis.</p>
                            <a href="#" class="btn primary">Read More</a>
                        </div>
                    </div>
                    <div class="case-study-card">
                        <div class="case-study-image">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="case-study-content">
                            <h4>Preventive Maintenance Optimization</h4>
                            <p>Reducing equipment failures by 65% through data-driven maintenance scheduling.</p>
                            <a href="#" class="btn primary">Read More</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="resources-section">
                <h3 class="section-title">Learning Resources</h3>
                <ul class="resources-list">
                    <li>
                        <i class="fas fa-book"></i>
                        <a href="#">Clinical Engineering Management Handbook</a>
                    </li>
                    <li>
                        <i class="fas fa-video"></i>
                        <a href="#">Video Tutorials on Equipment Maintenance</a>
                    </li>
                    <li>
                        <i class="fas fa-file-alt"></i>
                        <a href="#">Best Practices in Risk Management</a>
                    </li>
                    <li>
                        <i class="fas fa-users"></i>
                        <a href="#">Clinical Engineering Staff Training Modules</a>
                    </li>
                    <li>
                        <i class="fas fa-tools"></i>
                        <a href="#">Equipment Troubleshooting Guides</a>
                    </li>
                </ul>
            </div>

            <div class="quiz-section">
                <div class="quiz-header">
                    <h3 class="quiz-title">Knowledge Assessment</h3>
                    <div class="quiz-progress">
                        <span>Question 1 of 10</span>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                    </div>
                </div>
                <div class="quiz-question">
                    <p class="question-text">What is the primary goal of a clinical engineering department in a hospital?</p>
                    <div class="answer-options">
                        <div class="answer-option">A. To reduce equipment costs</div>
                        <div class="answer-option">B. To ensure safe and effective operation of medical equipment</div>
                        <div class="answer-option">C. To train medical staff on equipment usage</div>
                        <div class="answer-option">D. To purchase new equipment</div>
                    </div>
                </div>
                <div class="quiz-navigation">
                    <button class="nav-btn secondary">Previous</button>
                    <button class="nav-btn primary">Next</button>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Virtual Hospital Training Platform</h3>
                    <p>An interactive educational platform for biomedical engineering students and professionals.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="facility-design.html">Healthcare Facility Design</a></li>
                        <li><a href="equipment-planning.html">Medical Equipment Planning</a></li>
                        <li><a href="clinical-management.html">Clinical Engineering Management</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <ul>
                        <li><a href="#">Documentation</a></li>
                        <li><a href="#">Tutorials</a></li>
                        <li><a href="#">Case Studies</a></li>
                        <li><a href="#">Research Papers</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Contact Us</h3>
                    <ul>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-map-marker-alt"></i> Sudan University of Science and Technology</li>
                        <li><i class="fas fa-phone"></i> +249 123 456 789</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Virtual Hospital Training Platform. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>