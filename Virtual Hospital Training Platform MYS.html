<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Hospital Training Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hospital-floor-plan {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600"><rect width="100%" height="100%" fill="%23f0f0f0"/><rect x="50" y="50" width="200" height="100" fill="%23a5d8ff" stroke="%23333"/><rect x="300" y="50" width="150" height="100" fill="%23a5d8ff" stroke="%23333"/><rect x="500" y="50" width="250" height="100" fill="%23a5d8ff" stroke="%23333"/><rect x="50" y="200" width="300" height="150" fill="%2374c0fc" stroke="%23333"/><rect x="400" y="200" width="350" height="150" fill="%2374c0fc" stroke="%23333"/><rect x="50" y="400" width="200" height="150" fill="%23338eda" stroke="%23333"/><rect x="300" y="400" width="200" height="150" fill="%23338eda" stroke="%23333"/><rect x="550" y="400" width="200" height="150" fill="%23338eda" stroke="%23333"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .equipment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .interactive-element {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .interactive-element:hover {
            background-color: #f8f9fa;
        }
        
        .simulation-area {
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            min-height: 300px;
        }
        
        .progress-tracker {
            position: relative;
        }
        
        .progress-tracker::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            height: 100%;
            width: 2px;
            background-color: #e9ecef;
            transform: translateX(-50%);
            z-index: 1;
        }
        
        @media (max-width: 768px) {
            .progress-tracker::after {
                left: 1.5rem;
            }
        }
        
        .tooltip {
            position: relative;
        }
        
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 10;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-blue-800 text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <i class="fas fa-hospital-alt text-3xl mr-3"></i>
                    <h1 class="text-2xl md:text-3xl font-bold">Virtual Hospital Training Platform</h1>
                </div>
                <nav class="flex space-x-4">
                    <a href="#facility-design" class="hover:text-blue-200 transition">Facility Design</a>
                    <a href="#equipment-planning" class="hover:text-blue-200 transition">Equipment Planning</a>
                    <a href="#clinical-engineering" class="hover:text-blue-200 transition">Clinical Engineering</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Empowering Future Biomedical Engineers</h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto">
                An interactive virtual training platform for mastering healthcare facility design, medical equipment planning, and clinical engineering management.
            </p>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <button class="bg-white text-blue-800 font-bold py-3 px-6 rounded-lg hover:bg-blue-100 transition">
                    Start Training <i class="fas fa-play ml-2"></i>
                </button>
                <button class="border-2 border-white font-bold py-3 px-6 rounded-lg hover:bg-blue-600 transition">
                    Take a Tour <i class="fas fa-info-circle ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Learning Paths -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">Comprehensive Training Modules</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Facility Design Card -->
                <div id="facility-design" class="bg-gray-50 rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="bg-blue-600 p-4 text-white">
                        <div class="flex items-center">
                            <i class="fas fa-ruler-combined text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">Healthcare Facility Design</h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="hospital-floor-plan h-48 mb-4"></div>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Hospital requirements analysis</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Space planning and functional relationships</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Healthcare design standards and regulations</span>
                            </li>
                        </ul>
                        <button class="mt-6 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition">
                            Explore Module <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Equipment Planning Card -->
                <div id="equipment-planning" class="bg-gray-50 rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="bg-green-600 p-4 text-white">
                        <div class="flex items-center">
                            <i class="fas fa-x-ray text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">Medical Equipment Planning</h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-center mb-4">
                            <div class="grid grid-cols-3 gap-2">
                                <i class="fas fa-procedures text-3xl text-green-600"></i>
                                <i class="fas fa-heartbeat text-3xl text-green-600"></i>
                                <i class="fas fa-brain text-3xl text-green-600"></i>
                                <i class="fas fa-microscope text-3xl text-green-600"></i>
                                <i class="fas fa-prescription-bottle-alt text-3xl text-green-600"></i>
                                <i class="fas fa-ambulance text-3xl text-green-600"></i>
                            </div>
                        </div>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Medical equipment selection criteria</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Technical specifications analysis</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Equipment planning project management</span>
                            </li>
                        </ul>
                        <button class="mt-6 w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition">
                            Explore Module <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Clinical Engineering Card -->
                <div id="clinical-engineering" class="bg-gray-50 rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                    <div class="bg-purple-600 p-4 text-white">
                        <div class="flex items-center">
                            <i class="fas fa-clipboard-check text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">Clinical Engineering</h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-center mb-4">
                            <div class="relative">
                                <i class="fas fa-tools text-5xl text-purple-600"></i>
                                <i class="fas fa-file-invoice-dollar text-3xl text-purple-400 absolute -right-2 -bottom-2"></i>
                            </div>
                        </div>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Technical and financial equipment evaluations</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Contract negotiation and management</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Clinical engineering department management</span>
                            </li>
                        </ul>
                        <button class="mt-6 w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition">
                            Explore Module <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Detailed Training Sections -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">Practical Skills Development</h2>
            
            <!-- Healthcare Facility Design Section -->
            <div class="mb-20">
                <div class="flex items-center mb-8">
                    <div class="bg-blue-100 p-3 rounded-full mr-4">
                        <i class="fas fa-ruler-combined text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800">Healthcare Facility Design</h3>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Key Learning Objectives</h4>
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <div class="space-y-4">
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('hospital-requirements')">
                                    <div class="bg-blue-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-clipboard-list text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Hospital Requirements Analysis</h5>
                                        <p class="text-gray-600 text-sm mt-1">Learn to assess clinical needs, patient flow, and operational requirements for different hospital departments.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('space-planning')">
                                    <div class="bg-blue-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-project-diagram text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Space Planning & Functional Relationships</h5>
                                        <p class="text-gray-600 text-sm mt-1">Master zoning strategies, adjacency requirements, and workflow optimization in healthcare facilities.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('design-standards')">
                                    <div class="bg-blue-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-gavel text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Design Standards & Regulations</h5>
                                        <p class="text-gray-600 text-sm mt-1">Understand international and local codes including infection control, accessibility, and safety requirements.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Interactive Simulation</h4>
                        <div class="simulation-area p-6 shadow-sm">
                            <div class="flex justify-between mb-4">
                                <h5 class="font-medium">Hospital Design Challenge</h5>
                                <button class="text-blue-600 text-sm font-medium">Reset <i class="fas fa-redo ml-1"></i></button>
                            </div>
                            <p class="text-sm text-gray-600 mb-4">Drag and drop departments to create an optimal hospital layout considering patient flow and functional relationships.</p>
                            
                            <div class="grid grid-cols-3 gap-2 mb-4">
                                <div class="bg-blue-100 p-2 text-center rounded cursor-move" draggable="true">
                                    <i class="fas fa-procedures text-blue-600"></i>
                                    <p class="text-xs mt-1">ER</p>
                                </div>
                                <div class="bg-blue-100 p-2 text-center rounded cursor-move" draggable="true">
                                    <i class="fas fa-flask text-blue-600"></i>
                                    <p class="text-xs mt-1">Lab</p>
                                </div>
                                <div class="bg-blue-100 p-2 text-center rounded cursor-move" draggable="true">
                                    <i class="fas fa-x-ray text-blue-600"></i>
                                    <p class="text-xs mt-1">Radiology</p>
                                </div>
                                <div class="bg-blue-100 p-2 text-center rounded cursor-move" draggable="true">
                                    <i class="fas fa-bed text-blue-600"></i>
                                    <p class="text-xs mt-1">Wards</p>
                                </div>
                                <div class="bg-blue-100 p-2 text-center rounded cursor-move" draggable="true">
                                    <i class="fas fa-user-md text-blue-600"></i>
                                    <p class="text-xs mt-1">OR</p>
                                </div>
                                <div class="bg-blue-100 p-2 text-center rounded cursor-move" draggable="true">
                                    <i class="fas fa-utensils text-blue-600"></i>
                                    <p class="text-xs mt-1">Kitchen</p>
                                </div>
                            </div>
                            
                            <div class="hospital-floor-plan h-48 border-2 border-dashed border-gray-300 rounded"></div>
                            
                            <button class="mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm transition">
                                Evaluate Design <i class="fas fa-chart-bar ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Medical Equipment Planning Section -->
            <div class="mb-20">
                <div class="flex items-center mb-8">
                    <div class="bg-green-100 p-3 rounded-full mr-4">
                        <i class="fas fa-x-ray text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800">Medical Equipment Planning</h3>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Key Learning Objectives</h4>
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <div class="space-y-4">
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('equipment-selection')">
                                    <div class="bg-green-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-filter text-green-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Equipment Selection</h5>
                                        <p class="text-gray-600 text-sm mt-1">Develop criteria for selecting appropriate medical equipment based on clinical needs, budget, and technology.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('technical-specs')">
                                    <div class="bg-green-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-file-alt text-green-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Technical Specifications</h5>
                                        <p class="text-gray-600 text-sm mt-1">Learn to analyze and define technical requirements for medical devices and systems.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('project-management')">
                                    <div class="bg-green-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-tasks text-green-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Project Management</h5>
                                        <p class="text-gray-600 text-sm mt-1">Master the planning, procurement, and implementation lifecycle of medical equipment projects.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Equipment Comparison Tool</h4>
                        <div class="simulation-area p-6 shadow-sm">
                            <div class="flex justify-between mb-4">
                                <h5 class="font-medium">MRI Scanner Selection</h5>
                                <div class="flex space-x-2">
                                    <button class="text-green-600 text-sm font-medium">Add Model <i class="fas fa-plus ml-1"></i></button>
                                    <button class="text-green-600 text-sm font-medium">Reset <i class="fas fa-redo ml-1"></i></button>
                                </div>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white rounded-lg overflow-hidden">
                                    <thead>
                                        <tr class="bg-green-50 text-green-800 text-left">
                                            <th class="py-2 px-3">Model</th>
                                            <th class="py-2 px-3">Field Strength</th>
                                            <th class="py-2 px-3">Features</th>
                                            <th class="py-2 px-3">Price</th>
                                            <th class="py-2 px-3"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-b border-gray-200 hover:bg-green-50">
                                            <td class="py-3 px-3">Model A</td>
                                            <td class="py-3 px-3">1.5T</td>
                                            <td class="py-3 px-3">
                                                <div class="tooltip">
                                                    <span class="text-green-600"><i class="fas fa-info-circle"></i> 5</span>
                                                    <div class="tooltip-text">Advanced imaging, Cardiac package, Neuro package, Wide bore, Silent scan</div>
                                                </div>
                                            </td>
                                            <td class="py-3 px-3">$1.2M</td>
                                            <td class="py-3 px-3">
                                                <button class="text-green-600 hover:text-green-800">
                                                    <i class="fas fa-chart-line"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="border-b border-gray-200 hover:bg-green-50">
                                            <td class="py-3 px-3">Model B</td>
                                            <td class="py-3 px-3">3.0T</td>
                                            <td class="py-3 px-3">
                                                <div class="tooltip">
                                                    <span class="text-green-600"><i class="fas fa-info-circle"></i> 7</span>
                                                    <div class="tooltip-text">Ultra-high resolution, Advanced cardiac, Advanced neuro, Wide bore, Silent scan, AI reconstruction, 70cm opening</div>
                                                </div>
                                            </td>
                                            <td class="py-3 px-3">$2.5M</td>
                                            <td class="py-3 px-3">
                                                <button class="text-green-600 hover:text-green-800">
                                                    <i class="fas fa-chart-line"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-green-50">
                                            <td class="py-3 px-3">Model C</td>
                                            <td class="py-3 px-3">1.5T</td>
                                            <td class="py-3 px-3">
                                                <div class="tooltip">
                                                    <span class="text-green-600"><i class="fas fa-info-circle"></i> 3</span>
                                                    <div class="tooltip-text">Basic imaging, Cardiac package, Wide bore</div>
                                                </div>
                                            </td>
                                            <td class="py-3 px-3">$850K</td>
                                            <td class="py-3 px-3">
                                                <button class="text-green-600 hover:text-green-800">
                                                    <i class="fas fa-chart-line"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <button class="mt-4 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm transition">
                                Compare Models <i class="fas fa-balance-scale ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Clinical Engineering Section -->
            <div>
                <div class="flex items-center mb-8">
                    <div class="bg-purple-100 p-3 rounded-full mr-4">
                        <i class="fas fa-clipboard-check text-purple-600 text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800">Clinical Engineering</h3>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Key Learning Objectives</h4>
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <div class="space-y-4">
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('equipment-evaluation')">
                                    <div class="bg-purple-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-search-dollar text-purple-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Equipment Evaluation</h5>
                                        <p class="text-gray-600 text-sm mt-1">Learn to conduct comprehensive technical and financial evaluations of medical equipment.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('contract-management')">
                                    <div class="bg-purple-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-file-signature text-purple-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Contract Management</h5>
                                        <p class="text-gray-600 text-sm mt-1">Develop negotiation skills and learn to manage service contracts, warranties, and SLAs.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start interactive-element p-3 rounded-lg" onclick="showDetails('department-management')">
                                    <div class="bg-purple-100 p-2 rounded-full mr-4">
                                        <i class="fas fa-sitemap text-purple-600"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">Department Management</h5>
                                        <p class="text-gray-600 text-sm mt-1">Understand how to establish and manage a clinical engineering department in hospitals.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Risk Management Simulation</h4>
                        <div class="simulation-area p-6 shadow-sm">
                            <div class="flex justify-between mb-4">
                                <h5 class="font-medium">Equipment Maintenance Scenario</h5>
                                <button class="text-purple-600 text-sm font-medium">New Scenario <i class="fas fa-random ml-1"></i></button>
                            </div>
                            
                            <div class="mb-4">
                                <p class="text-sm text-gray-700 mb-2">Hospital has 15-year-old anesthesia machines with increasing failure rates:</p>
                                <ul class="text-sm text-gray-600 list-disc pl-5 space-y-1">
                                    <li>3 critical failures in last 6 months</li>
                                    <li>Average repair cost: $5,000 per incident</li>
                                    <li>Downtime: 3-5 days per repair</li>
                                    <li>Replacement cost: $45,000 per unit</li>
                                </ul>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <input type="radio" id="option1" name="risk-option" class="mr-3">
                                    <label for="option1" class="text-sm">Continue with current maintenance contract</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" id="option2" name="risk-option" class="mr-3">
                                    <label for="option2" class="text-sm">Upgrade to comprehensive service plan</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" id="option3" name="risk-option" class="mr-3">
                                    <label for="option3" class="text-sm">Replace oldest 50% of machines</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" id="option4" name="risk-option" class="mr-3">
                                    <label for="option4" class="text-sm">Full replacement of all machines</label>
                                </div>
                            </div>
                            
                            <button class="mt-4 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm transition">
                                Analyze Risk & Cost <i class="fas fa-calculator ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Learning Journey -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">Your Clinical Engineering Journey</h2>
            
            <div class="relative progress-tracker">
                <!-- Step 1 -->
                <div class="relative mb-8 pl-12 md:pl-0 md:flex">
                    <div class="hidden md:flex md:flex-shrink-0 md:justify-center md:w-16">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-600 text-white z-10">
                            <span>1</span>
                        </div>
                    </div>
                    <div class="md:pl-6 md:pr-8">
                        <div class="flex md:hidden absolute left-0 top-0 -ml-1 w-10 h-10 rounded-full bg-blue-600 text-white items-center justify-center z-10">
                            <span>1</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Foundation Knowledge</h3>
                        <p class="text-gray-600">
                            Build understanding of healthcare systems, medical equipment basics, and facility design principles through interactive modules and case studies.
                        </p>
                    </div>
                </div>
                
                <!-- Step 2 -->
                <div class="relative mb-8 pl-12 md:pl-0 md:flex">
                    <div class="hidden md:flex md:flex-shrink-0 md:justify-center md:w-16">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-600 text-white z-10">
                            <span>2</span>
                        </div>
                    </div>
                    <div class="md:pl-6 md:pr-8">
                        <div class="flex md:hidden absolute left-0 top-0 -ml-1 w-10 h-10 rounded-full bg-green-600 text-white items-center justify-center z-10">
                            <span>2</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Technical Skills Development</h3>
                        <p class="text-gray-600">
                            Master equipment evaluation, technical specifications analysis, and project planning through hands-on simulations and virtual labs.
                        </p>
                    </div>
                </div>
                
                <!-- Step 3 -->
                <div class="relative mb-8 pl-12 md:pl-0 md:flex">
                    <div class="hidden md:flex md:flex-shrink-0 md:justify-center md:w-16">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-purple-600 text-white z-10">
                            <span>3</span>
                        </div>
                    </div>
                    <div class="md:pl-6 md:pr-8">
                        <div class="flex md:hidden absolute left-0 top-0 -ml-1 w-10 h-10 rounded-full bg-purple-600 text-white items-center justify-center z-10">
                            <span>3</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Management Competencies</h3>
                        <p class="text-gray-600">
                            Develop negotiation, contract management, and department leadership skills through scenario-based learning and role-playing exercises.
                        </p>
                    </div>
                </div>
                
                <!-- Step 4 -->
                <div class="relative pl-12 md:pl-0 md:flex">
                    <div class="hidden md:flex md:flex-shrink-0 md:justify-center md:w-16">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-yellow-500 text-white z-10">
                            <span>4</span>
                        </div>
                    </div>
                    <div class="md:pl-6 md:pr-8">
                        <div class="flex md:hidden absolute left-0 top-0 -ml-1 w-10 h-10 rounded-full bg-yellow-500 text-white items-center justify-center z-10">
                            <span>4</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Capstone Project</h3>
                        <p class="text-gray-600">
                            Apply all learned skills in a comprehensive virtual hospital project, from facility design to equipment planning and clinical engineering management.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">What Our Students Say</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user-graduate text-blue-600 text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">Ahmed M.</h4>
                            <p class="text-sm text-gray-500">Biomedical Engineering Student</p>
                        </div>
                    </div>
                    <p class="text-gray-600">
                        "The virtual hospital platform transformed how I understand healthcare facility design. The interactive simulations made complex concepts click instantly."
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                
                <!-- Testimonial 2 -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user-md text-green-600 text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">Dr. Sarah K.</h4>
                            <p class="text-sm text-gray-500">Hospital Administrator</p>
                        </div>
                    </div>
                    <p class="text-gray-600">
                        "As someone who hires biomedical engineers, I'm impressed by how well this platform prepares students for real-world clinical engineering challenges."
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                
                <!-- Testimonial 3 -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4">
                            <i class="fas fa-user-tie text-purple-600 text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold">Mohammed R.</h4>
                            <p class="text-sm text-gray-500">Clinical Engineer</p>
                        </div>
                    </div>
                    <p class="text-gray-600">
                        "The equipment planning simulations mirror exactly what I do daily. This would have given me such a head start in my career!"
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-blue-800 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">Ready to Transform Your Biomedical Engineering Career?</h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto">
                Join our virtual hospital training platform and gain the practical skills needed to excel in healthcare facility design, medical equipment planning, and clinical engineering.
            </p>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <button class="bg-white text-blue-800 font-bold py-3 px-8 rounded-lg hover:bg-blue-100 transition text-lg">
                    Enroll Now <i class="fas fa-user-graduate ml-2"></i>
                </button>
                <button class="border-2 border-white font-bold py-3 px-8 rounded-lg hover:bg-blue-700 transition text-lg">
                    Request Demo <i class="fas fa-laptop-medical ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fas fa-hospital-alt mr-2"></i> Virtual Hospital
                    </h3>
                    <p class="text-gray-400">
                        Empowering the next generation of biomedical engineers with practical, interactive training in healthcare facility design and clinical engineering.
                    </p>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Home</a></li>
                        <li><a href="#facility-design" class="text-gray-400 hover:text-white transition">Facility Design</a></li>
                        <li><a href="#equipment-planning" class="text-gray-400 hover:text-white transition">Equipment Planning</a></li>
                        <li><a href="#clinical-engineering" class="text-gray-400 hover:text-white transition">Clinical Engineering</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Resources</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Case Studies</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Industry Standards</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Equipment Database</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Regulatory Guidelines</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact Us</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2 text-gray-400"></i>
                            <span><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-2 text-gray-400"></i>
                            <span>+************</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-gray-400"></i>
                            <span>123 Engineering St, Tech City</span>
                        </li>
                    </ul>
                    <div class="flex space-x-4 mt-4">
                        <a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 Virtual Hospital Training Platform. All rights reserved.</p>
                <p class="mt-2">Author: Dr. Mohammed Yagoub Esmail, SUST - BME</p>
                <p class="mt-1">Contact: <EMAIL></p>
                <p class="mt-1">Phone: +249912867327, +966538076790</p>
            </div>
        </div>
    </footer>

    <script>
        function showDetails(module) {
            // In a real implementation, this would show detailed content for the selected module
            alert(`Loading detailed content for: ${module.replace('-', ' ')}`);
        }
        
        // Simple drag and drop functionality for the hospital design simulation
        document.addEventListener('DOMContentLoaded', function() {
            const draggables = document.querySelectorAll('[draggable="true"]');
            
            draggables.forEach(draggable => {
                draggable.addEventListener('dragstart', () => {
                    draggable.classList.add('opacity-50');
                });
                
                draggable.addEventListener('dragend', () => {
                    draggable.classList.remove('opacity-50');
                });
            });
            
            const dropZone = document.querySelector('.hospital-floor-plan');
            dropZone.addEventListener('dragover', e => {
                e.preventDefault();
                dropZone.classList.add('border-blue-500');
            });
            
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('border-blue-500');
            });
            
            dropZone.addEventListener('drop', e => {
                e.preventDefault();
                dropZone.classList.remove('border-blue-500');
                // In a real implementation, this would handle the drop logic
                console.log('Dropped element in the hospital floor plan');
            });
        });
    </script>
</body>
</html>