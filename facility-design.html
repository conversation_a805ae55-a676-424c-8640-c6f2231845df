<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Healthcare Facility Design - Virtual Hospital Training Platform</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .module-content {
            padding: 120px 0 80px;
        }

        .module-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .module-header h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .module-header p {
            max-width: 800px;
            margin: 0 auto;
            font-size: 1.1rem;
            color: #555;
        }

        .design-simulator {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 40px;
        }

        .simulator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .simulator-title {
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .simulator-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .control-btn:hover {
            background-color: #0052a3;
        }

        .design-canvas {
            height: 500px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            position: relative;
            background-color: #f9f9f9;
            overflow: hidden;
        }

        .design-tools {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: var(--light-color);
            border-radius: 8px;
        }

        .tool-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: var(--transition);
            width: 100px;
        }

        .tool-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .tool-item i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        .tool-item span {
            font-size: 0.9rem;
            text-align: center;
        }

        .learning-section {
            margin-top: 60px;
        }

        .learning-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .tab-btn {
            padding: 12px 25px;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 500;
            color: #555;
            cursor: pointer;
            position: relative;
            transition: var(--transition);
        }

        .tab-btn.active {
            color: var(--primary-color);
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary-color);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .case-studies {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .case-study-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        .case-study-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .case-study-image {
            height: 200px;
            background-color: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .case-study-image i {
            font-size: 4rem;
            color: var(--primary-color);
        }

        .case-study-content {
            padding: 20px;
        }

        .case-study-content h3 {
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        .case-study-content p {
            color: #555;
            margin-bottom: 15px;
        }

        .resources-list {
            list-style-type: none;
        }

        .resources-list li {
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .resources-list li:last-child {
            border-bottom: none;
        }

        .resources-list li i {
            margin-right: 15px;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .resources-list li a {
            flex: 1;
            color: var(--text-color);
            font-weight: 500;
        }

        .resources-list li a:hover {
            color: var(--primary-color);
        }

        .quiz-section {
            margin-top: 40px;
            background-color: var(--light-color);
            padding: 30px;
            border-radius: 10px;
        }

        .quiz-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .quiz-title {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .quiz-progress {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .progress-bar {
            width: 200px;
            height: 10px;
            background-color: #ddd;
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--secondary-color);
            width: 25%;
        }

        .quiz-question {
            background-color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .question-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
            color: var(--dark-color);
        }

        .answer-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .answer-option {
            padding: 15px;
            border: 2px solid #eee;
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition);
        }

        .answer-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(0, 102, 204, 0.05);
        }

        .quiz-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .nav-btn {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .nav-btn:hover {
            background-color: #0052a3;
        }

        .nav-btn.secondary {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .nav-btn.secondary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            .simulator-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .design-tools {
                justify-content: center;
            }

            .quiz-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .progress-bar {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <i class="fas fa-hospital"></i>
                <h1>Virtual Hospital Training Platform</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="facility-design.html" class="active">Healthcare Facility Design</a></li>
                    <li><a href="equipment-planning.html">Medical Equipment Planning</a></li>
                    <li><a href="clinical-management.html">Clinical Engineering Management</a></li>
                    <li><a href="#" class="lang-toggle">العربية</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <section class="module-content">
        <div class="container">
            <div class="module-header">
                <h2>Healthcare Facility Design</h2>
                <p>Master hospital layout planning, patient flow optimization, and architectural requirements through interactive simulations and case studies.</p>
            </div>

            <div class="design-simulator">
                <div class="simulator-header">
                    <h3 class="simulator-title">Hospital Layout Designer</h3>
                    <div class="simulator-controls">
                        <button class="control-btn"><i class="fas fa-save"></i> Save</button>
                        <button class="control-btn"><i class="fas fa-undo"></i> Reset</button>
                        <button class="control-btn"><i class="fas fa-share"></i> Share</button>
                    </div>
                </div>

                <div class="design-tools">
                    <div class="tool-item">
                        <i class="fas fa-bed"></i>
                        <span>Patient Room</span>
                    </div>
                    <div class="tool-item">
                        <i class="fas fa-user-md"></i>
                        <span>Doctor Office</span>
                    </div>
                    <div class="tool-item">
                        <i class="fas fa-procedures"></i>
                        <span>Operating Room</span>
                    </div>
                    <div class="tool-item">
                        <i class="fas fa-x-ray"></i>
                        <span>Imaging</span>
                    </div>
                    <div class="tool-item">
                        <i class="fas fa-vial"></i>
                        <span>Laboratory</span>
                    </div>
                    <div class="tool-item">
                        <i class="fas fa-ambulance"></i>
                        <span>Emergency</span>
                    </div>
                    <div class="tool-item">
                        <i class="fas fa-door-open"></i>
                        <span>Entrance</span>
                    </div>
                    <div class="tool-item">
                        <i class="fas fa-restroom"></i>
                        <span>Restroom</span>
                    </div>
                </div>

                <div class="design-canvas">
                    <p style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #999; font-size: 1.2rem;">Drag and drop elements to design your hospital layout</p>
                </div>
            </div>

            <div class="learning-section">
                <div class="learning-tabs">
                    <button class="tab-btn active" data-tab="concepts">Design Concepts</button>
                    <button class="tab-btn" data-tab="case-studies">Case Studies</button>
                    <button class="tab-btn" data-tab="resources">Resources</button>
                    <button class="tab-btn" data-tab="quiz">Knowledge Check</button>
                </div>

                <div class="tab-content active" id="concepts">
                    <h3>Key Design Concepts</h3>
                    <p>Healthcare facility design requires careful consideration of multiple factors to create environments that support healing, efficiency, and safety. The following concepts form the foundation of effective hospital design.</p>

                    <div class="concept-cards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 30px;">
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <h3>Patient Flow Optimization</h3>
                            <p>Designing efficient pathways for patient movement from admission to discharge, minimizing travel distances and reducing cross-traffic between clinical and public areas.</p>
                        </div>
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3>Infection Control</h3>
                            <p>Implementing design strategies that minimize the risk of healthcare-associated infections through proper ventilation, material selection, and zoning of clean and contaminated areas.</p>
                        </div>
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-universal-access"></i>
                            </div>
                            <h3>Accessibility</h3>
                            <p>Ensuring that all areas of the facility are accessible to people with disabilities, including proper door widths, corridor dimensions, and wayfinding systems.</p>
                        </div>
                        <div class="card">
                            <div class="card-icon">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </div>
                            <h3>Flexibility & Adaptability</h3>
                            <p>Creating spaces that can adapt to changing healthcare needs, technologies, and treatment methods without requiring major renovations.</p>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="case-studies">
                    <h3>Real-World Case Studies</h3>
                    <p>Explore these examples of successful healthcare facility designs and the challenges overcome during implementation.</p>

                    <div class="case-studies">
                        <div class="case-study-card">
                            <div class="case-study-image">
                                <i class="fas fa-hospital-alt"></i>
                            </div>
                            <div class="case-study-content">
                                <h3>Urban Medical Center Expansion</h3>
                                <p>A case study on expanding a 200-bed urban hospital while maintaining full operations, focusing on phasing construction and minimizing disruption to patient care.</p>
                                <a href="#" class="btn primary">View Details</a>
                            </div>
                        </div>
                        <div class="case-study-card">
                            <div class="case-study-image">
                                <i class="fas fa-clinic-medical"></i>
                            </div>
                            <div class="case-study-content">
                                <h3>Rural Healthcare Facility</h3>
                                <p>Designing a sustainable rural healthcare facility with limited resources, incorporating telemedicine capabilities and modular construction techniques.</p>
                                <a href="#" class="btn primary">View Details</a>
                            </div>
                        </div>
                        <div class="case-study-card">
                            <div class="case-study-image">
                                <i class="fas fa-virus"></i>
                            </div>
                            <div class="case-study-content">
                                <h3>Pandemic Response Unit</h3>
                                <p>Rapid design and implementation of a specialized infectious disease treatment unit during a public health crisis, focusing on isolation protocols and staff safety.</p>
                                <a href="#" class="btn primary">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="resources">
                    <h3>Learning Resources</h3>
                    <p>Access these valuable resources to deepen your understanding of healthcare facility design principles and best practices.</p>

                    <ul class="resources-list">
                        <li>
                            <i class="fas fa-book"></i>
                            <a href="#">Healthcare Facility Design Guidelines - Complete Manual</a>
                        </li>
                        <li>
                            <i class="fas fa-video"></i>
                            <a href="#">Video Series: Hospital Design Masterclass</a>
                        </li>
                        <li>
                            <i class="fas fa-file-pdf"></i>
                            <a href="#">Patient Flow Optimization: Strategies and Solutions</a>
                        </li>
                        <li>
                            <i class="fas fa-file-pdf"></i>
                            <a href="#">Infection Control in Healthcare Construction</a>
                        </li>
                        <li>
                            <i class="fas fa-podcast"></i>
                            <a href="#">Podcast: The Future of Healthcare Architecture</a>
                        </li>
                        <li>
                            <i class="fas fa-external-link-alt"></i>
                            <a href="#">External Resource: International Health Facility Guidelines</a>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="quiz">
                    <div class="quiz-section">
                        <div class="quiz-header">
                            <h3 class="quiz-title">Knowledge Check: Healthcare Facility Design</h3>
                            <div class="quiz-progress">
                                <span>1 of 4</span>
                                <div class="progress-bar">
                                    <div class="progress-fill"></div>
                                </div>
                            </div>
                        </div>

                        <div class="quiz-question">
                            <p class="question-text">What is the primary consideration when designing patient flow in a healthcare facility?</p>
                            <div class="answer-options">
                                <div class="answer-option">Aesthetic appeal of corridors</div>
                                <div class="answer-option">Minimizing travel distance and reducing cross-traffic</div>
                                <div class="answer-option">Maximizing the number of waiting areas</div>
                                <div class="answer-option">Prioritizing staff convenience over patient needs</div>
                            </div>
                        </div>

                        <div class="quiz-navigation">
                            <button class="nav-btn secondary">Previous</button>
                            <button class="nav-btn">Next Question</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Virtual Hospital Training Platform</h3>
                    <p>An interactive educational system for biomedical engineering students and professionals.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Training Modules</h3>
                    <ul>
                        <li><a href="facility-design.html">Healthcare Facility Design</a></li>
                        <li><a href="equipment-planning.html">Medical Equipment Planning</a></li>
                        <li><a href="clinical-management.html">Clinical Engineering Management</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <ul>
                        <li><a href="#">Case Studies</a></li>
                        <li><a href="#">Research Papers</a></li>
                        <li><a href="#">Video Tutorials</a></li>
                        <li><a href="#">Glossary</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Contact</h3>
                    <ul>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-map-marker-alt"></i> Sudan University of Science and Technology</li>
                        <li><i class="fas fa-phone"></i> +249 123 456 789</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Virtual Hospital Training Platform. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script>
        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // Remove active class from all buttons and contents
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // Add active class to clicked button and corresponding content
                    this.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Tool item functionality
            const toolItems = document.querySelectorAll('.tool-item');
            const designCanvas = document.querySelector('.design-canvas');

            toolItems.forEach(tool => {
                tool.addEventListener('click', function() {
                    const toolName = this.querySelector('span').textContent;
                    const toolIcon = this.querySelector('i').className;

                    // Create a new element in the design canvas
                    const newElement = document.createElement('div');
                    newElement.className = 'canvas-element';
                    newElement.style.position = 'absolute';
                    newElement.style.left = '50px';
                    newElement.style.top = '50px';
                    newElement.style.padding = '10px';
                    newElement.style.backgroundColor = 'white';
                    newElement.style.borderRadius = '5px';
                    newElement.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
                    newElement.style.cursor = 'move';
                    newElement.innerHTML = `<i class="${toolIcon}" style="font-size: 1.5rem; color: var(--primary-color);"></i><p style="margin: 5px 0 0; font-size: 0.8rem;">${toolName}</p>`;

                    // Remove placeholder text if it exists
                    const placeholder = designCanvas.querySelector('p');
                    if (placeholder) {
                        placeholder.remove();
                    }

                    // Add element to canvas
                    designCanvas.appendChild(newElement);

                    // Make element draggable
                    makeElementDraggable(newElement);
                });
            });

            function makeElementDraggable(element) {
                let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

                element.onmousedown = dragMouseDown;

                function dragMouseDown(e) {
                    e = e || window.event;
                    e.preventDefault();
                    // Get the mouse cursor position at startup
                    pos3 = e.clientX;
                    pos4 = e.clientY;
                    document.onmouseup = closeDragElement;
                    // Call a function whenever the cursor moves
                    document.onmousemove = elementDrag;
                }

                function elementDrag(e) {
                    e = e || window.event;
                    e.preventDefault();
                    // Calculate the new cursor position
                    pos1 = pos3 - e.clientX;
                    pos2 = pos4 - e.clientY;
                    pos3 = e.clientX;
                    pos4 = e.clientY;
                    // Set the element's new position
                    element.style.top = (element.offsetTop - pos2) + "px";
                    element.style.left = (element.offsetLeft - pos1) + "px";
                }

                function closeDragElement() {
                    // Stop moving when mouse button is released
                    document.onmouseup = null;
                    document.onmousemove = null;
                }
            }

            // Quiz functionality
            const answerOptions = document.querySelectorAll('.answer-option');
            const nextBtn = document.querySelector('.quiz-navigation .nav-btn:last-child');
            const prevBtn = document.querySelector('.quiz-navigation .nav-btn:first-child');
            const progressFill = document.querySelector('.progress-fill');
            const questionCounter = document.querySelector('.quiz-progress span');

            answerOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove selected class from all options
                    answerOptions.forEach(opt => opt.classList.remove('selected'));

                    // Add selected class to clicked option
                    this.classList.add('selected');
                    this.style.borderColor = 'var(--primary-color)';
                    this.style.backgroundColor = 'rgba(0, 102, 204, 0.1)';
                });
            });

            // Sample questions data (in a real application, this would come from a database or API)
            const questions = [
                {
                    question: "What is the primary consideration when designing patient flow in a healthcare facility?",
                    options: [
                        "Aesthetic appeal of corridors",
                        "Minimizing travel distance and reducing cross-traffic",
                        "Maximizing the number of waiting areas",
                        "Prioritizing staff convenience over patient needs"
                    ],
                    correctAnswer: 1
                },
                {
                    question: "Which design feature is most important for infection control?",
                    options: [
                        "Expensive artwork in waiting areas",
                        "Proper ventilation systems and zoning of clean/contaminated areas",
                        "Large windows in patient rooms",
                        "Multiple entrances for visitors"
                    ],
                    correctAnswer: 1
                },
                {
                    question: "What does 'universal design' in healthcare facilities refer to?",
                    options: [
                        "Using the same design in all hospitals worldwide",
                        "Creating spaces accessible to people with diverse abilities",
                        "Designing facilities that can be used for any medical specialty",
                        "Implementing the latest design trends regardless of cost"
                    ],
                    correctAnswer: 1
                },
                {
                    question: "Why is flexibility important in healthcare facility design?",
                    options: [
                        "It allows facilities to adapt to changing medical technologies and practices",
                        "It reduces construction costs",
                        "It makes cleaning easier",
                        "It improves the facility's appearance"
                    ],
                    correctAnswer: 0
                }
            ];

            let currentQuestion = 0;

            nextBtn.addEventListener('click', function() {
                if (currentQuestion < questions.length - 1) {
                    currentQuestion++;
                    updateQuestion();
                } else {
                    // Show results
                    showQuizResults();
                }
            });

            prevBtn.addEventListener('click', function() {
                if (currentQuestion > 0) {
                    currentQuestion--;
                    updateQuestion();
                }
            });

            function updateQuestion() {
                const question = questions[currentQuestion];
                const questionText = document.querySelector('.question-text');

                // Update question text
                questionText.textContent = question.question;

                // Update options
                const optionsContainer = document.querySelector('.answer-options');
                optionsContainer.innerHTML = '';

                question.options.forEach((option, index) => {
                    const optionElement = document.createElement('div');
                    optionElement.className = 'answer-option';
                    optionElement.textContent = option;

                    optionElement.addEventListener('click', function() {
                        // Remove selected class from all options
                        document.querySelectorAll('.answer-option').forEach(opt => {
                            opt.classList.remove('selected');
                            opt.style.borderColor = '#eee';
                            opt.style.backgroundColor = 'transparent';
                        });

                        // Add selected class to clicked option
                        this.classList.add('selected');
                        this.style.borderColor = 'var(--primary-color)';
                        this.style.backgroundColor = 'rgba(0, 102, 204, 0.1)';
                    });

                    optionsContainer.appendChild(optionElement);
                });

                // Update progress
                const progress = ((currentQuestion + 1) / questions.length) * 100;
                progressFill.style.width = `${progress}%`;
                questionCounter.textContent = `${currentQuestion + 1} of ${questions.length}`;

                // Update navigation buttons
                prevBtn.style.display = currentQuestion === 0 ? 'none' : 'block';
                nextBtn.textContent = currentQuestion === questions.length - 1 ? 'Finish Quiz' : 'Next Question';
            }

            function showQuizResults() {
                const quizSection = document.querySelector('.quiz-section');
                quizSection.innerHTML = `
                    <div class="quiz-header">
                        <h3 class="quiz-title">Quiz Results</h3>
                    </div>
                    <div class="quiz-results" style="text-align: center; padding: 40px 0;">
                        <i class="fas fa-trophy" style="font-size: 4rem; color: var(--secondary-color); margin-bottom: 20px;"></i>
                        <h3 style="font-size: 2rem; margin-bottom: 15px;">Congratulations!</h3>
                        <p style="font-size: 1.2rem; margin-bottom: 30px;">You've successfully completed the Healthcare Facility Design knowledge check.</p>
                        <p style="font-size: 1.1rem; margin-bottom: 30px;">Your score: 3 out of 4 (75%)</p>
                        <div style="display: flex; justify-content: center; gap: 15px;">
                            <button class="nav-btn">Review Answers</button>
                            <button class="nav-btn secondary">Retake Quiz</button>
                        </div>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>